#!/usr/bin/env python3
"""
Test script for the Trading Dataframe Management System
Tests all signal formats and dataframe operations
"""

import sys
import os
import asyncio
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils_new.signal_handler import <PERSON>Handler
from utils_new.api_handler import TradeSignal


def test_signal_formats():
    """Test different signal formats and dataframe updates"""
    
    print("🧪 Testing Trading Dataframe Management System")
    print("=" * 60)
    
    # Initialize signal handler (which includes dataframe manager)
    signal_handler = SignalHandler()
    
    # Test signals based on the 5 formats mentioned in memories
    test_signals = [
        # Format 1: BUY trade entry
        "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$152.9$146.3$2025-09-12 14:33:00+05:30",
        
        # Format 2: INTIMATION/hold message
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 CALL at current price: 151.2 (Entry price: 152.9) (Stop loss: 146.3)",
        
        # Format 4: Stop loss update
        "===Algo_Trading===$Update$STOP LOSS to$154.08$for option$NIFTY 16 SEP 25000 CALL current price: 157.8 (Entry price: 155.3)",
        
        # Format 3: CLOSE trade with exit reason
        "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$157.4$2025-09-12 14:35:00+05:30$stop_loss_exit$157.4",
        
        # Format 5: CLOSE trade with crossover exit
        "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$155.3$150.0$2025-09-12 15:00:00+05:30",
        "===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81500 PUT$82.35$2025-09-08 15:15:00+05:30$opposite_crossover_exit"
    ]
    
    return signal_handler, test_signals


async def run_tests():
    """Run comprehensive tests"""
    
    signal_handler, test_signals = test_signal_formats()
    
    print("\n📊 Testing Signal Processing and Dataframe Updates")
    print("-" * 50)
    
    for i, signal_text in enumerate(test_signals, 1):
        print(f"\n🔄 Test {i}: Processing signal")
        print(f"Signal: {signal_text}")
        
        # Process the signal
        result = await signal_handler.handle_telegram_message(signal_text)
        
        print(f"Result: {result['status']} - {result['message']}")
        
        # Show current active trades after each signal
        active_trades = signal_handler.get_active_trades()
        print(f"Active trades count: {active_trades.get('total_active_trades', 0)}")
        
        if active_trades.get('instruments'):
            print(f"Active instruments: {active_trades['instruments']}")
            
            # Show details for each active trade
            for instrument, details in active_trades.get('trades_detail', {}).items():
                print(f"  📈 {instrument}:")
                print(f"    Entry: {details['entry_price']} at {details['entry_time']}")
                if details['is_closed']:
                    print(f"    Exit: {details['exit_price']} at {details['exit_time']}")
                    print(f"    P&L: {details['profit_loss']}")
                else:
                    print(f"    Status: Open")
        
        print("-" * 30)
    
    print("\n📋 Final Summary")
    print("=" * 30)
    
    # Get final summary
    final_summary = signal_handler.get_active_trades()
    print(f"Total active trades: {final_summary.get('total_active_trades', 0)}")
    
    if final_summary.get('trades_detail'):
        print("\nTrade Details:")
        for instrument, details in final_summary['trades_detail'].items():
            print(f"\n🔹 {instrument}")
            print(f"  Entry Price: {details['entry_price']}")
            print(f"  Entry Time: {details['entry_time']}")
            if details['is_closed']:
                print(f"  Exit Price: {details['exit_price']}")
                print(f"  Exit Time: {details['exit_time']}")
                print(f"  P&L: {details['profit_loss']}")
                print(f"  Status: ✅ Closed")
            else:
                print(f"  Status: 🔄 Open")
    
    print("\n✅ All tests completed!")


def test_dataframe_structure():
    """Test the dataframe structure directly"""
    
    print("\n🔬 Testing Dataframe Structure")
    print("-" * 40)
    
    signal_handler, _ = test_signal_formats()
    
    # Create a test signal
    test_signal = TradeSignal(
        symbol="NIFTY",
        action="BUY",
        strike=25000,
        option_type="CE",
        expiry_date="16 SEP",
        price=150.0,
        timestamp=datetime.now(),
        signal_type="TRADE"
    )
    
    # Process the signal
    result = signal_handler.dataframe_manager.create_or_update_trade_entry(test_signal)
    print(f"Entry creation result: {result}")
    
    # Get the dataframe
    instrument_name = "NIFTY 16 SEP 25000 CALL"
    df = signal_handler.get_trade_dataframe(instrument_name)
    
    if df is not None:
        print(f"\n📊 Dataframe for {instrument_name}:")
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print("\nData:")
        print(df.to_string())
        
        # Test exit signal
        exit_signal = TradeSignal(
            symbol="NIFTY",
            action="CLOSE",
            strike=25000,
            option_type="CE",
            expiry_date="16 SEP",
            price=160.0,
            timestamp=datetime.now(),
            signal_type="TRADE"
        )
        
        exit_result = signal_handler.dataframe_manager.update_trade_exit(exit_signal)
        print(f"\nExit update result: {exit_result}")
        
        # Show updated dataframe
        updated_df = signal_handler.get_trade_dataframe(instrument_name)
        print(f"\n📊 Updated Dataframe:")
        print(updated_df.to_string())
    
    else:
        print("❌ No dataframe found!")


if __name__ == "__main__":
    print("🚀 Starting Trading Dataframe Management System Tests")
    
    # Test dataframe structure first
    test_dataframe_structure()
    
    # Run comprehensive signal tests
    asyncio.run(run_tests())
    
    print("\n🎉 Testing completed successfully!")
