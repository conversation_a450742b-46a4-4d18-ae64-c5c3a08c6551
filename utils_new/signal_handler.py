import re
from datetime import datetime
from typing import Optional, Dict, Any
from .api_handler import APIHandler, TradeSignal
from .logger import logger
from .trading_dataframe_manager import TradingDataframeManager

class SignalHandler:
    def __init__(self):
        self.logger = logger.get_logger('trade')
        self.api = APIHandler()
        self.dataframe_manager = TradingDataframeManager()

    def _parse_option_chain(self, option_chain: str) -> Dict[str, Any]:
        """
        Parse option chain string like 'SENSEX 18 SEP 81500 PUT' or 'NIFTY 16 SEP 25000 PUT'
        Returns dict with symbol, expiry_date, strike, option_type
        """
        try:
            parts = option_chain.strip().split()
            if len(parts) < 4:
                return {}

            symbol = parts[0]
            expiry_date = f"{parts[1]} {parts[2]}"  # e.g., "18 SEP"
            strike = float(parts[3])
            option_type = parts[4] if len(parts) > 4 else None

            # Convert CALL/PUT to CE/PE if needed
            if option_type == "CALL":
                option_type = "CE"
            elif option_type == "PUT":
                option_type = "PE"

            return {
                "symbol": symbol,
                "expiry_date": expiry_date,
                "strike": strike,
                "option_type": option_type
            }
        except Exception as e:
            self.logger.error(f"Error parsing option chain '{option_chain}': {e}")
            return {}

    def _parse_algo_trading_message(self, message: str) -> Optional[TradeSignal]:
        """
        Parse Algo_Trading format messages
        Handles formats:
        1. ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$152.9$146.3$2025-09-12 14:33:00+05:30
        2. ===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 18 SEP 81500 PUT at current price: 151.2 (Entry price: 152.9) (Stop loss: 146.3)
        3. ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81500 PUT$157.4$2025-09-12 14:35:00+05:30$stop_loss_exit$157.4
        4. ===Algo_Trading===$Update$STOP LOSS to$154.08$for option$SENSEX 18 SEP 81500 PUT current price: 157.8 (Entry price: 155.3)
        5. ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 24700 PUT$82.35$2025-09-08 15:15:00+05:30$opposite_crossover_exit
        """
        try:
            if not message.startswith("===Algo_Trading==="):
                return None

            # Remove the prefix
            content = message.replace("===Algo_Trading===$", "")

            # Split by $ to get main components
            parts = content.split("$")

            if len(parts) < 2:
                return None

            signal_type = parts[0]  # TRADE, INTIMATION, Update

            if signal_type == "TRADE":
                return self._parse_trade_signal(parts)
            elif signal_type == "INTIMATION":
                return self._parse_intimation_signal(parts)
            elif signal_type == "Update":
                return self._parse_update_signal(parts)
            else:
                self.logger.warning(f"Unknown signal type: {signal_type}")
                return None

        except Exception as e:
            self.logger.error(f"Error parsing algo trading message: {e}")
            return None

    def _parse_trade_signal(self, parts: list) -> Optional[TradeSignal]:
        """Parse TRADE signals (BUY/SELL/CLOSE)"""
        try:
            if len(parts) < 4:
                return None

            action = parts[1]  # BUY, SELL, CLOSE
            option_chain = parts[2]  # e.g., "SENSEX 18 SEP 81500 PUT"

            # Parse option chain
            option_info = self._parse_option_chain(option_chain)
            if not option_info:
                return None

            signal = TradeSignal(
                symbol=option_info["symbol"],
                action=action,
                strike=option_info["strike"],
                option_type=option_info["option_type"],
                expiry_date=option_info["expiry_date"],
                signal_type="TRADE"
            )

            if action in ["BUY", "SELL"]:
                # Format: TRADE$BUY$SENSEX 18 SEP 81500 PUT$152.9$146.3$2025-09-12 14:33:00+05:30
                if len(parts) >= 6:
                    signal.price = float(parts[3])
                    signal.entry_price = float(parts[3])
                    signal.stop_loss = float(parts[4])

                    # Parse timestamp if available
                    if len(parts) >= 6:
                        timestamp_str = parts[5]
                        try:
                            signal.timestamp = datetime.fromisoformat(timestamp_str)
                        except:
                            signal.timestamp = datetime.now()

            elif action == "CLOSE":
                # Format: TRADE$CLOSE$SENSEX 18 SEP 81500 PUT$157.4$2025-09-12 14:35:00+05:30$stop_loss_exit$157.4
                # or: TRADE$CLOSE$NIFTY 16 SEP 24700 PUT$82.35$2025-09-08 15:15:00+05:30$opposite_crossover_exit
                if len(parts) >= 4:
                    signal.exit_price = float(parts[3])
                    signal.price = float(parts[3])

                    # Parse timestamp if available
                    if len(parts) >= 5:
                        timestamp_str = parts[4]
                        try:
                            signal.timestamp = datetime.fromisoformat(timestamp_str)
                        except:
                            signal.timestamp = datetime.now()

                    # Parse exit reason if available
                    if len(parts) >= 6:
                        signal.exit_reason = parts[5]

                    # Some formats have exit price repeated at the end
                    if len(parts) >= 7:
                        try:
                            # If the last part is a number, it might be exit price
                            float(parts[6])
                            signal.exit_price = float(parts[6])
                            signal.price = float(parts[6])
                        except ValueError:
                            # If not a number, it's probably part of exit reason
                            pass

            return signal

        except Exception as e:
            self.logger.error(f"Error parsing trade signal: {e}")
            return None

    def _parse_intimation_signal(self, parts: list) -> Optional[TradeSignal]:
        """Parse INTIMATION signals"""
        try:
            # Format: INTIMATION$Continue to hold trade for $SENSEX 18 SEP 81500 PUT at current price: 151.2 (Entry price: 152.9) (Stop loss: 146.3)
            if len(parts) < 3:
                return None

            option_chain = parts[2].split(" at current price:")[0].strip()

            # Parse option chain
            option_info = self._parse_option_chain(option_chain)
            if not option_info:
                return None

            signal = TradeSignal(
                symbol=option_info["symbol"],
                action="HOLD",
                strike=option_info["strike"],
                option_type=option_info["option_type"],
                expiry_date=option_info["expiry_date"],
                signal_type="INTIMATION"
            )

            # Extract prices from the message using regex
            full_message = "$".join(parts)

            # Extract current price
            current_price_match = re.search(r'current price:\s*([\d.]+)', full_message)
            if current_price_match:
                signal.current_price = float(current_price_match.group(1))
                signal.price = float(current_price_match.group(1))

            # Extract entry price
            entry_price_match = re.search(r'Entry price:\s*([\d.]+)', full_message)
            if entry_price_match:
                signal.entry_price = float(entry_price_match.group(1))

            # Extract stop loss
            stop_loss_match = re.search(r'Stop loss:\s*([\d.]+)', full_message)
            if stop_loss_match:
                signal.stop_loss = float(stop_loss_match.group(1))

            return signal

        except Exception as e:
            self.logger.error(f"Error parsing intimation signal: {e}")
            return None

    def _parse_update_signal(self, parts: list) -> Optional[TradeSignal]:
        """Parse UPDATE signals"""
        try:
            # Format: Update$STOP LOSS to$154.08$for option$SENSEX 18 SEP 81500 PUT current price: 157.8 (Entry price: 155.3)
            # or: Update$STOP LOSS to$30.01$for option$NIFTY 16 SEP 25000 PUT
            if len(parts) < 5:
                return None

            update_type = parts[1]  # "STOP LOSS to"
            new_value = float(parts[2])  # New stop loss value
            option_chain = parts[4].split(" current price:")[0].strip()

            # Parse option chain
            option_info = self._parse_option_chain(option_chain)
            if not option_info:
                return None

            signal = TradeSignal(
                symbol=option_info["symbol"],
                action="UPDATE",
                strike=option_info["strike"],
                option_type=option_info["option_type"],
                expiry_date=option_info["expiry_date"],
                signal_type="UPDATE",
                update_type=update_type.replace(" to", "").strip(),
                new_stop_loss=new_value
            )

            # Extract additional prices if available
            full_message = "$".join(parts)

            # Extract current price
            current_price_match = re.search(r'current price:\s*([\d.]+)', full_message)
            if current_price_match:
                signal.current_price = float(current_price_match.group(1))
                signal.price = float(current_price_match.group(1))

            # Extract entry price
            entry_price_match = re.search(r'Entry price:\s*([\d.]+)', full_message)
            if entry_price_match:
                signal.entry_price = float(entry_price_match.group(1))

            return signal

        except Exception as e:
            self.logger.error(f"Error parsing update signal: {e}")
            return None

    def _parse_simple_message(self, message: str) -> Optional[TradeSignal]:
        """
        Parse simple format messages like:
        - BUY NIFTY 19500 CE
        - SELL BANKNIFTY 45000 PE
        - EXIT NIFTY 19500 CE
        - BUY RELIANCE
        - SELL SBIN
        """
        try:
            # Remove extra whitespace and convert to uppercase
            parts = message.strip().upper().split()

            if not parts:
                return None

            # Extract action (BUY/SELL/EXIT)
            action = parts[0]
            if action not in ['BUY', 'SELL', 'EXIT']:
                return None

            # Extract symbol
            symbol = parts[1]

            # Check if it's an options trade
            if len(parts) == 4:
                strike = float(parts[2])
                option_type = parts[3]
                if option_type not in ['CE', 'PE']:
                    return None

                return TradeSignal(
                    symbol=symbol,
                    action=action,
                    strike=strike,
                    option_type=option_type,
                    timestamp=datetime.now()
                )
            elif len(parts) == 2:
                # Stock/Index trade
                return TradeSignal(
                    symbol=symbol,
                    action=action,
                    timestamp=datetime.now()
                )
            else:
                return None

        except Exception as e:
            self.logger.error(f"Error parsing simple message: {e}")
            return None

    def _parse_message(self, message: str) -> Optional[TradeSignal]:
        """
        Main message parser that handles all formats
        """
        try:
            # Check if it's an Algo_Trading format
            if message.startswith("===Algo_Trading==="):
                return self._parse_algo_trading_message(message)
            else:
                # Try simple format
                return self._parse_simple_message(message)

        except Exception as e:
            self.logger.error(f"Error parsing message: {e}")
            return None

    async def handle_telegram_message(self, message: str) -> Dict[str, Any]:
        """Process incoming telegram message"""
        try:
            # Log incoming message
            self.logger.info(f"Processing message: {message}")

            # Parse the message
            signal = self._parse_message(message)
            if not signal:
                return {
                    "status": "error",
                    "message": "Invalid signal format. Supported formats:\n" +
                             "1. ===Algo_Trading===$TRADE$BUY$SYMBOL DD MMM STRIKE PUT/CALL$PRICE$STOP_LOSS$TIMESTAMP\n" +
                             "2. ===Algo_Trading===$INTIMATION$Continue to hold...\n" +
                             "3. ===Algo_Trading===$TRADE$CLOSE$SYMBOL DD MMM STRIKE PUT/CALL$PRICE$TIMESTAMP$REASON\n" +
                             "4. ===Algo_Trading===$Update$STOP LOSS to$PRICE$for option$SYMBOL...\n" +
                             "5. Simple format: 'ACTION SYMBOL [STRIKE] [CE/PE]'"
                }

            # Log parsed signal details
            self.logger.info(f"Parsed signal: {signal}")

            # Process signal through dataframe manager first
            dataframe_result = self.dataframe_manager.process_signal(signal)

            # Log dataframe processing result
            if dataframe_result["status"] == "success":
                self.logger.info(f"Dataframe updated: {dataframe_result['message']}")
            else:
                self.logger.error(f"Dataframe update failed: {dataframe_result['message']}")

            # Handle different signal types
            if signal.signal_type == "INTIMATION":
                # For intimation signals, return dataframe result
                return dataframe_result
            elif signal.signal_type == "UPDATE":
                # For update signals, return dataframe result
                return dataframe_result
            else:
                # For trade signals, also process through API if needed
                # Note: API processing is optional and can be enabled/disabled
                api_result = self.api.process_signal(signal)

                # Log the API result
                if api_result["status"] == "success":
                    self.logger.info(f"API signal processed successfully: {api_result}")
                else:
                    self.logger.error(f"API signal processing failed: {api_result}")

                # Return combined result with dataframe info taking priority
                combined_result = {
                    "status": dataframe_result["status"],
                    "message": f"Dataframe: {dataframe_result['message']} | API: {api_result['message']}",
                    "dataframe_result": dataframe_result,
                    "api_result": api_result
                }

                return combined_result

        except Exception as e:
            error_msg = f"Message handling failed: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def get_active_trades(self) -> Dict[str, Any]:
        """Get summary of all active trades from dataframe manager"""
        return self.dataframe_manager.get_trade_summary()

    def get_trade_dataframe(self, instrument_name: str) -> Optional[Any]:
        """Get specific trade dataframe for an instrument"""
        return self.dataframe_manager.get_active_trade(instrument_name)

    def remove_completed_trade(self, instrument_name: str) -> bool:
        """Remove a completed trade from active tracking"""
        return self.dataframe_manager.remove_completed_trade(instrument_name)

    def get_daily_csv_path(self) -> str:
        """Get the path to today's CSV trade log file"""
        return self.dataframe_manager.get_daily_csv_path()

    def get_completed_trades_count(self) -> int:
        """Get the number of completed trades logged today"""
        return self.dataframe_manager.get_csv_trade_count()
