import pandas as pd
from datetime import datetime
from typing import Dict, Optional, Any
from .logger import logger
from .api_handler import TradeSignal


class TradingDataframeManager:
    """
    Global trading dataframe management system that maintains one active dataframe 
    per trading instrument, where each dataframe tracks a single trade lifecycle 
    from entry to exit.
    """
    
    def __init__(self):
        self.logger = logger.get_logger('trading_dataframes')
        # Dictionary to store active dataframes: {instrument_name: DataFrame}
        self.active_dataframes: Dict[str, pd.DataFrame] = {}
        self.logger.info("Trading Dataframe Manager initialized")
    
    def _create_empty_dataframe(self) -> pd.DataFrame:
        """Create an empty dataframe with the required structure"""
        columns = [
            'Instrument_name',           # Complete option chain name
            'trade_entry_time',          # Timestamp when trade signal was received
            'trade_exit_time',           # Timestamp when close signal was received (initially null)
            'Trade_entry_price_sig',     # Entry price from trading signal
            'Trade_exit_price_sig',      # Exit price from trading signal (initially null)
            'profit_loss_sig',           # Calculated P&L based on signal prices (initially null)
            'Trade_entry_price_act',     # Actual executed entry price (placeholder)
            'Trade_exit_price_act',      # Actual executed exit price (placeholder)
            'profit_loss_act'            # Actual P&L from executed trades (placeholder)
        ]
        
        return pd.DataFrame(columns=columns)
    
    def _get_instrument_name(self, signal: TradeSignal) -> str:
        """
        Generate standardized instrument name from signal
        Examples: 
        - "NIFTY24DEC21000CE" 
        - "NIFTY 16 SEP 25000 CALL"
        - "SENSEX 18 SEP 81500 PUT"
        """
        if signal.strike and signal.option_type and signal.expiry_date:
            # Option instrument
            option_type_full = "CALL" if signal.option_type == "CE" else "PUT"
            return f"{signal.symbol} {signal.expiry_date} {int(signal.strike)} {option_type_full}"
        elif signal.strike and signal.option_type:
            # Option without expiry date
            option_type_full = "CALL" if signal.option_type == "CE" else "PUT"
            return f"{signal.symbol}{int(signal.strike)}{signal.option_type}"
        else:
            # Stock/Index instrument
            return signal.symbol
    
    def create_or_update_trade_entry(self, signal: TradeSignal) -> Dict[str, Any]:
        """
        Create new dataframe or update existing one for trade entry (BUY/SELL signals)
        """
        try:
            instrument_name = self._get_instrument_name(signal)
            
            # Check if there's already an active trade for this instrument
            if instrument_name in self.active_dataframes:
                self.logger.warning(f"Active trade already exists for {instrument_name}. Overwriting with new entry.")
            
            # Create new dataframe for this trade
            df = self._create_empty_dataframe()
            
            # Add the trade entry data
            trade_data = {
                'Instrument_name': instrument_name,
                'trade_entry_time': signal.timestamp or datetime.now(),
                'trade_exit_time': None,
                'Trade_entry_price_sig': signal.price or signal.entry_price,
                'Trade_exit_price_sig': None,
                'profit_loss_sig': None,
                'Trade_entry_price_act': None,  # Placeholder for future implementation
                'Trade_exit_price_act': None,   # Placeholder for future implementation
                'profit_loss_act': None        # Placeholder for future implementation
            }
            
            # Add row to dataframe
            df.loc[0] = trade_data
            
            # Store in active dataframes
            self.active_dataframes[instrument_name] = df
            
            self.logger.info(f"Created new trade entry for {instrument_name} at price {trade_data['Trade_entry_price_sig']}")
            
            return {
                "status": "success",
                "message": f"Trade entry created for {instrument_name}",
                "instrument_name": instrument_name,
                "entry_price": trade_data['Trade_entry_price_sig'],
                "entry_time": trade_data['trade_entry_time']
            }
            
        except Exception as e:
            error_msg = f"Failed to create trade entry: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}
    
    def update_trade_exit(self, signal: TradeSignal) -> Dict[str, Any]:
        """
        Update existing dataframe with trade exit information (CLOSE signals)
        """
        try:
            instrument_name = self._get_instrument_name(signal)
            
            # Check if there's an active trade for this instrument
            if instrument_name not in self.active_dataframes:
                error_msg = f"No active trade found for {instrument_name} to close"
                self.logger.error(error_msg)
                return {"status": "error", "message": error_msg}
            
            df = self.active_dataframes[instrument_name]
            
            # Update the exit information
            df.loc[0, 'trade_exit_time'] = signal.timestamp or datetime.now()
            df.loc[0, 'Trade_exit_price_sig'] = signal.price or signal.exit_price
            
            # Calculate P&L if both entry and exit prices are available
            entry_price = df.loc[0, 'Trade_entry_price_sig']
            exit_price = df.loc[0, 'Trade_exit_price_sig']
            
            if entry_price is not None and exit_price is not None:
                # Assuming BUY entry (profit = exit - entry), adjust logic as needed
                profit_loss = float(exit_price) - float(entry_price)
                df.loc[0, 'profit_loss_sig'] = profit_loss
            
            self.logger.info(f"Updated trade exit for {instrument_name} at price {exit_price}")
            
            return {
                "status": "success",
                "message": f"Trade exit updated for {instrument_name}",
                "instrument_name": instrument_name,
                "exit_price": exit_price,
                "exit_time": df.loc[0, 'trade_exit_time'],
                "profit_loss": df.loc[0, 'profit_loss_sig']
            }
            
        except Exception as e:
            error_msg = f"Failed to update trade exit: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def handle_intimation_signal(self, signal: TradeSignal) -> Dict[str, Any]:
        """
        Handle INTIMATION signals (hold messages) - no dataframe updates needed
        """
        try:
            instrument_name = self._get_instrument_name(signal)

            # Check if there's an active trade for this instrument
            if instrument_name not in self.active_dataframes:
                self.logger.warning(f"Received intimation for {instrument_name} but no active trade found")
                return {
                    "status": "warning",
                    "message": f"Intimation received for {instrument_name} but no active trade found"
                }

            self.logger.info(f"Intimation received for {instrument_name} - continuing to hold")

            return {
                "status": "success",
                "message": f"Intimation processed for {instrument_name}",
                "instrument_name": instrument_name,
                "current_price": signal.current_price,
                "entry_price": signal.entry_price,
                "stop_loss": signal.stop_loss
            }

        except Exception as e:
            error_msg = f"Failed to handle intimation signal: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def handle_update_signal(self, signal: TradeSignal) -> Dict[str, Any]:
        """
        Handle UPDATE signals (stop loss updates) - no dataframe updates needed for now
        """
        try:
            instrument_name = self._get_instrument_name(signal)

            # Check if there's an active trade for this instrument
            if instrument_name not in self.active_dataframes:
                self.logger.warning(f"Received update for {instrument_name} but no active trade found")
                return {
                    "status": "warning",
                    "message": f"Update received for {instrument_name} but no active trade found"
                }

            self.logger.info(f"Update received for {instrument_name}: {signal.update_type} to {signal.new_stop_loss}")

            return {
                "status": "success",
                "message": f"Update processed for {instrument_name}",
                "instrument_name": instrument_name,
                "update_type": signal.update_type,
                "new_value": signal.new_stop_loss,
                "current_price": signal.current_price
            }

        except Exception as e:
            error_msg = f"Failed to handle update signal: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def get_active_trade(self, instrument_name: str) -> Optional[pd.DataFrame]:
        """
        Get the active dataframe for a specific instrument
        """
        return self.active_dataframes.get(instrument_name)

    def get_all_active_trades(self) -> Dict[str, pd.DataFrame]:
        """
        Get all active trading dataframes
        """
        return self.active_dataframes.copy()

    def remove_completed_trade(self, instrument_name: str) -> bool:
        """
        Remove a completed trade from active dataframes
        """
        try:
            if instrument_name in self.active_dataframes:
                del self.active_dataframes[instrument_name]
                self.logger.info(f"Removed completed trade for {instrument_name}")
                return True
            else:
                self.logger.warning(f"No active trade found for {instrument_name} to remove")
                return False
        except Exception as e:
            self.logger.error(f"Failed to remove trade for {instrument_name}: {e}")
            return False

    def get_trade_summary(self) -> Dict[str, Any]:
        """
        Get summary of all active trades
        """
        try:
            summary = {
                "total_active_trades": len(self.active_dataframes),
                "instruments": list(self.active_dataframes.keys()),
                "trades_detail": {}
            }

            for instrument_name, df in self.active_dataframes.items():
                if not df.empty:
                    trade_info = {
                        "entry_time": df.loc[0, 'trade_entry_time'],
                        "entry_price": df.loc[0, 'Trade_entry_price_sig'],
                        "exit_time": df.loc[0, 'trade_exit_time'],
                        "exit_price": df.loc[0, 'Trade_exit_price_sig'],
                        "profit_loss": df.loc[0, 'profit_loss_sig'],
                        "is_closed": df.loc[0, 'trade_exit_time'] is not None
                    }
                    summary["trades_detail"][instrument_name] = trade_info

            return summary

        except Exception as e:
            self.logger.error(f"Failed to generate trade summary: {e}")
            return {"error": str(e)}

    def process_signal(self, signal: TradeSignal) -> Dict[str, Any]:
        """
        Main method to process any type of trading signal and update dataframes accordingly
        """
        try:
            self.logger.info(f"Processing signal: {signal.signal_type} - {signal.action} for {signal.symbol}")

            if signal.signal_type == "TRADE":
                if signal.action.upper() in ["BUY", "SELL"]:
                    # Entry signal
                    return self.create_or_update_trade_entry(signal)
                elif signal.action.upper() == "CLOSE":
                    # Exit signal
                    return self.update_trade_exit(signal)
                else:
                    return {"status": "error", "message": f"Unknown trade action: {signal.action}"}

            elif signal.signal_type == "INTIMATION":
                # Hold signal
                return self.handle_intimation_signal(signal)

            elif signal.signal_type == "UPDATE":
                # Update signal (stop loss, etc.)
                return self.handle_update_signal(signal)

            else:
                return {"status": "error", "message": f"Unknown signal type: {signal.signal_type}"}

        except Exception as e:
            error_msg = f"Failed to process signal: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}
