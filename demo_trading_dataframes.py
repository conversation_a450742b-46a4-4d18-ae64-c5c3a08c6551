#!/usr/bin/env python3
"""
Demo script showing how to use the Trading Dataframe Management System
"""

import sys
import os
import asyncio
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils_new.signal_handler import SignalHandler


async def demo_trading_dataframes():
    """Demonstrate the trading dataframe management system"""
    
    print("🚀 Trading Dataframe Management System Demo")
    print("=" * 60)
    
    # Initialize signal handler
    signal_handler = SignalHandler()
    
    print("\n📊 System Features:")
    print("✅ One dataframe per trading instrument")
    print("✅ Single row per dataframe (current active trade)")
    print("✅ Complete trade lifecycle tracking (entry to exit)")
    print("✅ Signal-based updates with 5 supported formats")
    print("✅ Automatic P&L calculation")
    print("✅ Placeholder columns for actual execution data")
    
    print("\n🔄 Processing Sample Trading Signals...")
    print("-" * 50)
    
    # Sample signals demonstrating the complete lifecycle
    signals = [
        # Entry signal
        "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$150.0$145.0$2025-09-14 10:00:00+05:30",
        
        # Hold signal
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 CALL at current price: 152.5 (Entry price: 150.0) (Stop loss: 145.0)",
        
        # Stop loss update
        "===Algo_Trading===$Update$STOP LOSS to$148.0$for option$NIFTY 16 SEP 25000 CALL current price: 155.0 (Entry price: 150.0)",
        
        # Exit signal
        "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$158.5$2025-09-14 15:30:00+05:30$profit_target_exit$158.5"
    ]
    
    for i, signal in enumerate(signals, 1):
        print(f"\n📈 Step {i}: {['Entry', 'Hold', 'Update', 'Exit'][i-1]} Signal")
        print(f"Signal: {signal}")
        
        result = await signal_handler.handle_telegram_message(signal)
        print(f"✅ Result: {result['status']} - {result['message']}")
        
        # Show current dataframe state
        trades = signal_handler.get_active_trades()
        if trades['total_active_trades'] > 0:
            for instrument, details in trades['trades_detail'].items():
                print(f"📊 {instrument}:")
                print(f"   Entry: ₹{details['entry_price']} at {details['entry_time']}")
                if details['is_closed']:
                    print(f"   Exit:  ₹{details['exit_price']} at {details['exit_time']}")
                    print(f"   P&L:   ₹{details['profit_loss']}")
                else:
                    print(f"   Status: Open")
    
    print("\n📋 Final Dataframe Structure:")
    print("-" * 40)
    
    # Get the actual dataframe to show structure
    df = signal_handler.get_trade_dataframe("NIFTY 16 SEP 25000 CALL")
    if df is not None:
        print("Columns:")
        for col in df.columns:
            print(f"  • {col}")
        
        print(f"\nDataframe Shape: {df.shape}")
        print("Sample Data:")
        print(df.to_string())
    
    print("\n🎯 Key Benefits:")
    print("✅ Real-time trade tracking")
    print("✅ Automatic P&L calculation")
    print("✅ Complete audit trail")
    print("✅ Easy integration with existing systems")
    print("✅ Scalable to multiple instruments")
    
    print("\n🔧 Available Methods:")
    print("• signal_handler.get_active_trades() - Get all active trades summary")
    print("• signal_handler.get_trade_dataframe(instrument) - Get specific trade dataframe")
    print("• signal_handler.remove_completed_trade(instrument) - Remove completed trade")
    
    print("\n✨ Demo completed successfully!")


if __name__ == "__main__":
    asyncio.run(demo_trading_dataframes())
