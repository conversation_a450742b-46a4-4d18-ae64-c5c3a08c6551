2025-09-14 09:49:09 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 09:49:09 - main - INFO - ✅ <PERSON><PERSON> started as <PERSON><PERSON><PERSON>ya_S_R
2025-09-14 09:49:09 - main - INFO - 🔍 Fetching dialogs to find the group...
2025-09-14 09:49:09 - main - INFO - ✅ Successfully found group 'ಆತ್ಮ ನಿರ್ಭರ'
2025-09-14 09:49:09 - main - INFO - 🔄 Listening for messages...
2025-09-14 09:49:57 - main - ERROR - ❌ Invalid signal format. Expected format: 'ACTION SYMBOL [STRIKE] [CE/PE]'
2025-09-14 10:23:20 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 10:23:20 - main - INFO - ✅ Bo<PERSON> started as Ka<PERSON>inya_S_R
2025-09-14 10:23:20 - main - INFO - 🔍 Fetching dialogs to find the group...
2025-09-14 10:23:21 - main - INFO - ✅ Successfully found group 'ಆತ್ಮ ನಿರ್ಭರ'
2025-09-14 10:23:21 - main - INFO - 🔄 Listening for messages...
2025-09-14 10:23:38 - main - ERROR - ❌ Instrument not found for NIFTY
2025-09-14 10:30:03 - main - INFO - 🚀 Starting Enhanced Algo Trading Bot...
2025-09-14 10:30:03 - main - INFO - ✅ Bot started as Kaundinya_S_R
2025-09-14 10:30:03 - main - INFO - 🔍 Fetching dialogs to find the group...
2025-09-14 10:30:04 - main - INFO - ✅ Successfully found group 'ಆತ್ಮ ನಿರ್ಭರ'
2025-09-14 10:30:04 - main - INFO - 🔄 Listening for messages...
[E 250914 10:30:22 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData. Error: Failed to get symbol details. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': 'e9:74:78:95:55:a3', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'exchange': 'NSE', 'tradingsymbol': 'NIFTY-Sep2025-25000-CE', 'symboltoken': '44668'}, Response: {'message': 'Failed to get symbol details', 'errorcode': 'AB1018', 'status': False, 'data': None}
2025-09-14 10:30:22 - main - ERROR - ❌ Unable to fetch market data
[E 250914 10:48:28 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData. Error: Failed to get symbol details. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': '3f:30:a6:63:b7:58', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'exchange': 'NSE', 'tradingsymbol': 'NIFTY-Sep2025-25000-CE', 'symboltoken': '44668'}, Response: {'message': 'Failed to get symbol details', 'errorcode': 'AB1018', 'status': False, 'data': None}
[E 250914 10:48:28 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData. Error: Failed to get symbol details. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': '3f:30:a6:63:b7:58', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'exchange': 'NSE', 'tradingsymbol': 'NIFTY-Sep2025-25000-CE', 'symboltoken': '44668'}, Response: {'message': 'Failed to get symbol details', 'errorcode': 'AB1018', 'status': False, 'data': None}
[E 250914 10:48:28 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData. Error: Failed to get symbol details. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': '3f:30:a6:63:b7:58', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'exchange': 'BSE', 'tradingsymbol': 'SENSEX-Sep2025-81500-PE', 'symboltoken': '862078'}, Response: {'message': 'Failed to get symbol details', 'errorcode': 'AB1018', 'status': False, 'data': None}
[E 250914 10:48:29 smartConnect:243] Error occurred while making a POST request to https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData. Error: Failed to get symbol details. URL: https://apiconnect.angelbroking.com/rest/secure/angelbroking/order/v1/getLtpData, Headers: {'Content-type': 'application/json', 'X-ClientLocalIP': '127.0.0.1', 'X-ClientPublicIP': '**************', 'X-MACAddress': '3f:30:a6:63:b7:58', 'Accept': 'application/json', 'X-PrivateKey': 'GvbeXvJX', 'X-UserType': 'USER', 'X-SourceID': 'WEB'}, Request: {'exchange': 'BSE', 'tradingsymbol': 'SENSEX-Sep2025-81500-PE', 'symboltoken': '862078'}, Response: {'message': 'Failed to get symbol details', 'errorcode': 'AB1018', 'status': False, 'data': None}
