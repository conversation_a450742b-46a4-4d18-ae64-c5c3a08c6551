2025-09-14 10:48:28 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$152.9$146.3$2025-09-12 14:33:00+05:30
2025-09-14 10:48:28 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=152.9, stop_loss=146.3, entry_price=152.9, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 10:48:28 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 10:48:28 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Unable to fetch market data'}
2025-09-14 10:48:28 - trade - INFO - Processing message: ===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 CALL at current price: 151.2 (Entry price: 152.9) (Stop loss: 146.3)
2025-09-14 10:48:28 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='HOLD', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 10, 48, 23, 558375), quantity=1, price=151.2, stop_loss=146.3, entry_price=152.9, exit_price=None, current_price=151.2, exit_reason=None, signal_type='INTIMATION', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 10:48:28 - trade - INFO - Dataframe updated: Intimation processed for NIFTY 16 SEP 25000 CALL
2025-09-14 10:48:28 - trade - INFO - Processing message: ===Algo_Trading===$Update$STOP LOSS to$154.08$for option$NIFTY 16 SEP 25000 CALL current price: 157.8 (Entry price: 155.3)
2025-09-14 10:48:28 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='UPDATE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 10, 48, 23, 558375), quantity=1, price=157.8, stop_loss=None, entry_price=155.3, exit_price=None, current_price=157.8, exit_reason=None, signal_type='UPDATE', expiry_date='16 SEP', update_type='STOP LOSS', new_stop_loss=154.08)
2025-09-14 10:48:28 - trade - INFO - Dataframe updated: Update processed for NIFTY 16 SEP 25000 CALL
2025-09-14 10:48:28 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$157.4$2025-09-12 14:35:00+05:30$stop_loss_exit$157.4
2025-09-14 10:48:28 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 35, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=157.4, stop_loss=None, entry_price=None, exit_price=157.4, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 10:48:28 - trade - INFO - Dataframe updated: Trade exit updated for NIFTY 16 SEP 25000 CALL
2025-09-14 10:48:28 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Unable to fetch market data'}
2025-09-14 10:48:28 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$155.3$150.0$2025-09-12 15:00:00+05:30
2025-09-14 10:48:28 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=81500.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 15, 0, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.3, stop_loss=150.0, entry_price=155.3, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 10:48:28 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 81500 PUT
2025-09-14 10:48:28 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Unable to fetch market data'}
2025-09-14 10:48:28 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81500 PUT$82.35$2025-09-08 15:15:00+05:30$opposite_crossover_exit
2025-09-14 10:48:28 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='CLOSE', strike=81500.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 8, 15, 15, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=82.35, stop_loss=None, entry_price=None, exit_price=82.35, current_price=None, exit_reason='opposite_crossover_exit', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 10:48:28 - trade - INFO - Dataframe updated: Trade exit updated for SENSEX 18 SEP 81500 PUT
2025-09-14 10:48:29 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Unable to fetch market data'}
